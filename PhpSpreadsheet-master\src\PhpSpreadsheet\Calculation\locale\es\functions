############################################################
##
## PhpSpreadsheet - function name translations
##
## Esp<PERSON>ñol (Spanish)
##
############################################################


##
## Funciones de cubo (Cube Functions)
##
CUBEKPIMEMBER = MIEMBROKPICUBO
CUBEMEMBER = MIEMBROCUBO
CUBEMEMBERPROPERTY = PROPIEDADMIEMBROCUBO
CUBERANKEDMEMBER = MIEMBRORANGOCUBO
CUBESET = CONJUNTOCUBO
CUBESETCOUNT = RECUENTOCONJUNTOCUBO
CUBEVALUE = VALORCUBO

##
## Funciones de base de datos (Database Functions)
##
DAVERAGE = BDPROMEDIO
DCOUNT = BDCONTAR
DCOUNTA = BDCONTARA
DGET = BDEXTRAER
DMAX = BDMAX
DMIN = BDMIN
DPRODUCT = BDPRODUCTO
DSTDEV = BDDESVEST
DSTDEVP = BDDESVESTP
DSUM = BDSUMA
DVAR = BDVAR
DVARP = BDVARP

##
## Funciones de fecha y hora (Date & Time Functions)
##
DATE = FECHA
DATEDIF = SIFECHA
DATESTRING = CADENA.FECHA
DATEVALUE = FECHANUMERO
DAY = DIA
DAYS = DIAS
DAYS360 = DIAS360
EDATE = FECHA.MES
EOMONTH = FIN.MES
HOUR = HORA
ISOWEEKNUM = ISO.NUM.DE.SEMANA
MINUTE = MINUTO
MONTH = MES
NETWORKDAYS = DIAS.LAB
NETWORKDAYS.INTL = DIAS.LAB.INTL
NOW = AHORA
SECOND = SEGUNDO
THAIDAYOFWEEK = DIASEMTAI
THAIMONTHOFYEAR = MESAÑOTAI
THAIYEAR = AÑOTAI
TIME = NSHORA
TIMEVALUE = HORANUMERO
TODAY = HOY
WEEKDAY = DIASEM
WEEKNUM = NUM.DE.SEMANA
WORKDAY = DIA.LAB
WORKDAY.INTL = DIA.LAB.INTL
YEAR = AÑO
YEARFRAC = FRAC.AÑO

##
## Funciones de ingeniería (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BIN.A.DEC
BIN2HEX = BIN.A.HEX
BIN2OCT = BIN.A.OCT
BITAND = BIT.Y
BITLSHIFT = BIT.DESPLIZQDA
BITOR = BIT.O
BITRSHIFT = BIT.DESPLDCHA
BITXOR = BIT.XO
COMPLEX = COMPLEJO
CONVERT = CONVERTIR
DEC2BIN = DEC.A.BIN
DEC2HEX = DEC.A.HEX
DEC2OCT = DEC.A.OCT
DELTA = DELTA
ERF = FUN.ERROR
ERF.PRECISE = FUN.ERROR.EXACTO
ERFC = FUN.ERROR.COMPL
ERFC.PRECISE = FUN.ERROR.COMPL.EXACTO
GESTEP = MAYOR.O.IGUAL
HEX2BIN = HEX.A.BIN
HEX2DEC = HEX.A.DEC
HEX2OCT = HEX.A.OCT
IMABS = IM.ABS
IMAGINARY = IMAGINARIO
IMARGUMENT = IM.ANGULO
IMCONJUGATE = IM.CONJUGADA
IMCOS = IM.COS
IMCOSH = IM.COSH
IMCOT = IM.COT
IMCSC = IM.CSC
IMCSCH = IM.CSCH
IMDIV = IM.DIV
IMEXP = IM.EXP
IMLN = IM.LN
IMLOG10 = IM.LOG10
IMLOG2 = IM.LOG2
IMPOWER = IM.POT
IMPRODUCT = IM.PRODUCT
IMREAL = IM.REAL
IMSEC = IM.SEC
IMSECH = IM.SECH
IMSIN = IM.SENO
IMSINH = IM.SENOH
IMSQRT = IM.RAIZ2
IMSUB = IM.SUSTR
IMSUM = IM.SUM
IMTAN = IM.TAN
OCT2BIN = OCT.A.BIN
OCT2DEC = OCT.A.DEC
OCT2HEX = OCT.A.HEX

##
## Funciones financieras (Financial Functions)
##
ACCRINT = INT.ACUM
ACCRINTM = INT.ACUM.V
AMORDEGRC = AMORTIZ.PROGRE
AMORLINC = AMORTIZ.LIN
COUPDAYBS = CUPON.DIAS.L1
COUPDAYS = CUPON.DIAS
COUPDAYSNC = CUPON.DIAS.L2
COUPNCD = CUPON.FECHA.L2
COUPNUM = CUPON.NUM
COUPPCD = CUPON.FECHA.L1
CUMIPMT = PAGO.INT.ENTRE
CUMPRINC = PAGO.PRINC.ENTRE
DB = DB
DDB = DDB
DISC = TASA.DESC
DOLLARDE = MONEDA.DEC
DOLLARFR = MONEDA.FRAC
DURATION = DURACION
EFFECT = INT.EFECTIVO
FV = VF
FVSCHEDULE = VF.PLAN
INTRATE = TASA.INT
IPMT = PAGOINT
IRR = TIR
ISPMT = INT.PAGO.DIR
MDURATION = DURACION.MODIF
MIRR = TIRM
NOMINAL = TASA.NOMINAL
NPER = NPER
NPV = VNA
ODDFPRICE = PRECIO.PER.IRREGULAR.1
ODDFYIELD = RENDTO.PER.IRREGULAR.1
ODDLPRICE = PRECIO.PER.IRREGULAR.2
ODDLYIELD = RENDTO.PER.IRREGULAR.2
PDURATION = P.DURACION
PMT = PAGO
PPMT = PAGOPRIN
PRICE = PRECIO
PRICEDISC = PRECIO.DESCUENTO
PRICEMAT = PRECIO.VENCIMIENTO
PV = VA
RATE = TASA
RECEIVED = CANTIDAD.RECIBIDA
RRI = RRI
SLN = SLN
SYD = SYD
TBILLEQ = LETRA.DE.TEST.EQV.A.BONO
TBILLPRICE = LETRA.DE.TES.PRECIO
TBILLYIELD = LETRA.DE.TES.RENDTO
VDB = DVS
XIRR = TIR.NO.PER
XNPV = VNA.NO.PER
YIELD = RENDTO
YIELDDISC = RENDTO.DESC
YIELDMAT = RENDTO.VENCTO

##
## Funciones de información (Information Functions)
##
CELL = CELDA
ERROR.TYPE = TIPO.DE.ERROR
INFO = INFO
ISBLANK = ESBLANCO
ISERR = ESERR
ISERROR = ESERROR
ISEVEN = ES.PAR
ISFORMULA = ESFORMULA
ISLOGICAL = ESLOGICO
ISNA = ESNOD
ISNONTEXT = ESNOTEXTO
ISNUMBER = ESNUMERO
ISODD = ES.IMPAR
ISREF = ESREF
ISTEXT = ESTEXTO
N = N
NA = NOD
SHEET = HOJA
SHEETS = HOJAS
TYPE = TIPO

##
## Funciones lógicas (Logical Functions)
##
AND = Y
FALSE = FALSO
IF = SI
IFERROR = SI.ERROR
IFNA = SI.ND
IFS = SI.CONJUNTO
NOT = NO
OR = O
SWITCH = CAMBIAR
TRUE = VERDADERO
XOR = XO

##
## Funciones de búsqueda y referencia (Lookup & Reference Functions)
##
ADDRESS = DIRECCION
AREAS = AREAS
CHOOSE = ELEGIR
COLUMN = COLUMNA
COLUMNS = COLUMNAS
FORMULATEXT = FORMULATEXTO
GETPIVOTDATA = IMPORTARDATOSDINAMICOS
HLOOKUP = BUSCARH
HYPERLINK = HIPERVINCULO
INDEX = INDICE
INDIRECT = INDIRECTO
LOOKUP = BUSCAR
MATCH = COINCIDIR
OFFSET = DESREF
ROW = FILA
ROWS = FILAS
RTD = RDTR
TRANSPOSE = TRANSPONER
VLOOKUP = BUSCARV
*RC = FC

##
## Funciones matemáticas y trigonométricas (Math & Trig Functions)
##
ABS = ABS
ACOS = ACOS
ACOSH = ACOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = AGREGAR
ARABIC = NUMERO.ARABE
ASIN = ASENO
ASINH = ASENOH
ATAN = ATAN
ATAN2 = ATAN2
ATANH = ATANH
BASE = BASE
CEILING.MATH = MULTIPLO.SUPERIOR.MAT
CEILING.PRECISE = MULTIPLO.SUPERIOR.EXACTO
COMBIN = COMBINAT
COMBINA = COMBINA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = CONV.DECIMAL
DEGREES = GRADOS
ECMA.CEILING = MULTIPLO.SUPERIOR.ECMA
EVEN = REDONDEA.PAR
EXP = EXP
FACT = FACT
FACTDOUBLE = FACT.DOBLE
FLOOR.MATH = MULTIPLO.INFERIOR.MAT
FLOOR.PRECISE = MULTIPLO.INFERIOR.EXACTO
GCD = M.C.D
INT = ENTERO
ISO.CEILING = MULTIPLO.SUPERIOR.ISO
LCM = M.C.M
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MDETERM
MINVERSE = MINVERSA
MMULT = MMULT
MOD = RESIDUO
MROUND = REDOND.MULT
MULTINOMIAL = MULTINOMIAL
MUNIT = M.UNIDAD
ODD = REDONDEA.IMPAR
PI = PI
POWER = POTENCIA
PRODUCT = PRODUCTO
QUOTIENT = COCIENTE
RADIANS = RADIANES
RAND = ALEATORIO
RANDBETWEEN = ALEATORIO.ENTRE
ROMAN = NUMERO.ROMANO
ROUND = REDONDEAR
ROUNDBAHTDOWN = REDONDEAR.BAHT.MAS
ROUNDBAHTUP = REDONDEAR.BAHT.MENOS
ROUNDDOWN = REDONDEAR.MENOS
ROUNDUP = REDONDEAR.MAS
SEC = SEC
SECH = SECH
SERIESSUM = SUMA.SERIES
SIGN = SIGNO
SIN = SENO
SINH = SENOH
SQRT = RAIZ
SQRTPI = RAIZ2PI
SUBTOTAL = SUBTOTALES
SUM = SUMA
SUMIF = SUMAR.SI
SUMIFS = SUMAR.SI.CONJUNTO
SUMPRODUCT = SUMAPRODUCTO
SUMSQ = SUMA.CUADRADOS
SUMX2MY2 = SUMAX2MENOSY2
SUMX2PY2 = SUMAX2MASY2
SUMXMY2 = SUMAXMENOSY2
TAN = TAN
TANH = TANH
TRUNC = TRUNCAR

##
## Funciones estadísticas (Statistical Functions)
##
AVEDEV = DESVPROM
AVERAGE = PROMEDIO
AVERAGEA = PROMEDIOA
AVERAGEIF = PROMEDIO.SI
AVERAGEIFS = PROMEDIO.SI.CONJUNTO
BETA.DIST = DISTR.BETA.N
BETA.INV = INV.BETA.N
BINOM.DIST = DISTR.BINOM.N
BINOM.DIST.RANGE = DISTR.BINOM.SERIE
BINOM.INV = INV.BINOM
CHISQ.DIST = DISTR.CHICUAD
CHISQ.DIST.RT = DISTR.CHICUAD.CD
CHISQ.INV = INV.CHICUAD
CHISQ.INV.RT = INV.CHICUAD.CD
CHISQ.TEST = PRUEBA.CHICUAD
CONFIDENCE.NORM = INTERVALO.CONFIANZA.NORM
CONFIDENCE.T = INTERVALO.CONFIANZA.T
CORREL = COEF.DE.CORREL
COUNT = CONTAR
COUNTA = CONTARA
COUNTBLANK = CONTAR.BLANCO
COUNTIF = CONTAR.SI
COUNTIFS = CONTAR.SI.CONJUNTO
COVARIANCE.P = COVARIANCE.P
COVARIANCE.S = COVARIANZA.M
DEVSQ = DESVIA2
EXPON.DIST = DISTR.EXP.N
F.DIST = DISTR.F.N
F.DIST.RT = DISTR.F.CD
F.INV = INV.F
F.INV.RT = INV.F.CD
F.TEST = PRUEBA.F.N
FISHER = FISHER
FISHERINV = PRUEBA.FISHER.INV
FORECAST.ETS = PRONOSTICO.ETS
FORECAST.ETS.CONFINT = PRONOSTICO.ETS.CONFINT
FORECAST.ETS.SEASONALITY = PRONOSTICO.ETS.ESTACIONALIDAD
FORECAST.ETS.STAT = PRONOSTICO.ETS.STAT
FORECAST.LINEAR = PRONOSTICO.LINEAL
FREQUENCY = FRECUENCIA
GAMMA = GAMMA
GAMMA.DIST = DISTR.GAMMA.N
GAMMA.INV = INV.GAMMA
GAMMALN = GAMMA.LN
GAMMALN.PRECISE = GAMMA.LN.EXACTO
GAUSS = GAUSS
GEOMEAN = MEDIA.GEOM
GROWTH = CRECIMIENTO
HARMEAN = MEDIA.ARMO
HYPGEOM.DIST = DISTR.HIPERGEOM.N
INTERCEPT = INTERSECCION.EJE
KURT = CURTOSIS
LARGE = K.ESIMO.MAYOR
LINEST = ESTIMACION.LINEAL
LOGEST = ESTIMACION.LOGARITMICA
LOGNORM.DIST = DISTR.LOGNORM
LOGNORM.INV = INV.LOGNORM
MAX = MAX
MAXA = MAXA
MAXIFS = MAX.SI.CONJUNTO
MEDIAN = MEDIANA
MIN = MIN
MINA = MINA
MINIFS = MIN.SI.CONJUNTO
MODE.MULT = MODA.VARIOS
MODE.SNGL = MODA.UNO
NEGBINOM.DIST = NEGBINOM.DIST
NORM.DIST = DISTR.NORM.N
NORM.INV = INV.NORM
NORM.S.DIST = DISTR.NORM.ESTAND.N
NORM.S.INV = INV.NORM.ESTAND
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTIL.EXC
PERCENTILE.INC = PERCENTIL.INC
PERCENTRANK.EXC = RANGO.PERCENTIL.EXC
PERCENTRANK.INC = RANGO.PERCENTIL.INC
PERMUT = PERMUTACIONES
PERMUTATIONA = PERMUTACIONES.A
PHI = FI
POISSON.DIST = POISSON.DIST
PROB = PROBABILIDAD
QUARTILE.EXC = CUARTIL.EXC
QUARTILE.INC = CUARTIL.INC
RANK.AVG = JERARQUIA.MEDIA
RANK.EQ = JERARQUIA.EQV
RSQ = COEFICIENTE.R2
SKEW = COEFICIENTE.ASIMETRIA
SKEW.P = COEFICIENTE.ASIMETRIA.P
SLOPE = PENDIENTE
SMALL = K.ESIMO.MENOR
STANDARDIZE = NORMALIZACION
STDEV.P = DESVEST.P
STDEV.S = DESVEST.M
STDEVA = DESVESTA
STDEVPA = DESVESTPA
STEYX = ERROR.TIPICO.XY
T.DIST = DISTR.T.N
T.DIST.2T = DISTR.T.2C
T.DIST.RT = DISTR.T.CD
T.INV = INV.T
T.INV.2T = INV.T.2C
T.TEST = PRUEBA.T.N
TREND = TENDENCIA
TRIMMEAN = MEDIA.ACOTADA
VAR.P = VAR.P
VAR.S = VAR.S
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = DISTR.WEIBULL
Z.TEST = PRUEBA.Z.N

##
## Funciones de texto (Text Functions)
##
BAHTTEXT = TEXTOBAHT
CHAR = CARACTER
CLEAN = LIMPIAR
CODE = CODIGO
CONCAT = CONCAT
DOLLAR = MONEDA
EXACT = IGUAL
FIND = ENCONTRAR
FIXED = DECIMAL
ISTHAIDIGIT = ESDIGITOTAI
LEFT = IZQUIERDA
LEN = LARGO
LOWER = MINUSC
MID = EXTRAE
NUMBERSTRING = CADENA.NUMERO
NUMBERVALUE = VALOR.NUMERO
PHONETIC = FONETICO
PROPER = NOMPROPIO
REPLACE = REEMPLAZAR
REPT = REPETIR
RIGHT = DERECHA
SEARCH = HALLAR
SUBSTITUTE = SUSTITUIR
T = T
TEXT = TEXTO
TEXTJOIN = UNIRCADENAS
THAIDIGIT = DIGITOTAI
THAINUMSOUND = SONNUMTAI
THAINUMSTRING = CADENANUMTAI
THAISTRINGLENGTH = LONGCADENATAI
TRIM = ESPACIOS
UNICHAR = UNICAR
UNICODE = UNICODE
UPPER = MAYUSC
VALUE = VALOR

##
## Funciones web (Web Functions)
##
ENCODEURL = URLCODIF
FILTERXML = XMLFILTRO
WEBSERVICE = SERVICIOWEB

##
## Funciones de compatibilidad (Compatibility Functions)
##
BETADIST = DISTR.BETA
BETAINV = DISTR.BETA.INV
BINOMDIST = DISTR.BINOM
CEILING = MULTIPLO.SUPERIOR
CHIDIST = DISTR.CHI
CHIINV = PRUEBA.CHI.INV
CHITEST = PRUEBA.CHI
CONCATENATE = CONCATENAR
CONFIDENCE = INTERVALO.CONFIANZA
COVAR = COVAR
CRITBINOM = BINOM.CRIT
EXPONDIST = DISTR.EXP
FDIST = DISTR.F
FINV = DISTR.F.INV
FLOOR = MULTIPLO.INFERIOR
FORECAST = PRONOSTICO
FTEST = PRUEBA.F
GAMMADIST = DISTR.GAMMA
GAMMAINV = DISTR.GAMMA.INV
HYPGEOMDIST = DISTR.HIPERGEOM
LOGINV = DISTR.LOG.INV
LOGNORMDIST = DISTR.LOG.NORM
MODE = MODA
NEGBINOMDIST = NEGBINOMDIST
NORMDIST = DISTR.NORM
NORMINV = DISTR.NORM.INV
NORMSDIST = DISTR.NORM.ESTAND
NORMSINV = DISTR.NORM.ESTAND.INV
PERCENTILE = PERCENTIL
PERCENTRANK = RANGO.PERCENTIL
POISSON = POISSON
QUARTILE = CUARTIL
RANK = JERARQUIA
STDEV = DESVEST
STDEVP = DESVESTP
TDIST = DISTR.T
TINV = DISTR.T.INV
TTEST = PRUEBA.T
VAR = VAR
VARP = VARP
WEIBULL = DIST.WEIBULL
ZTEST = PRUEBA.Z
